import React, { useState, useEffect, useRef } from "react";
import { Controller } from "react-hook-form";
import { Card, CardContent, CardHeader } from "@base/card";
import { RadioGroup, RadioGroupItem } from "@base/radio-group";
import { Label } from "@base/label";
import { Checkbox } from "@base/checkbox";
import { Badge } from "@commons/badge";
import { Eye, EyeOff, XIcon } from "lucide-react";
import { usePaymentMethods } from "@/hooks/usePaymentMethods";
import { useTranslation } from "@hooks/useTranslation";
import { PaymentMethodForm } from "@/components/payment-methods/payment-method-form";
import { PaymentMethodType } from "@/types/payment-methods";
import { MOBILE_BANKING_OPTIONS } from "@/pages/checkout/constants";
import { InputField } from "@commons/form/input-field";
import { formatCreditCard, formatExpiryDate } from "@/utils/mask";
import { getCardBrand } from "@/utils/validation";
import type { PaymentMethodProps } from "./types";
import type { CreditCardFormData } from "@/types/payment-methods";
import {
  handleCardClick,
  getPaymentMethodErrorMessage,
  getPaymentMethodIconPath,
  getMobileBankingIconPath,
  getSavedMethodDisplayText,
  animateCardExpand,
} from "./helpers";

const PaymentMethod: React.FC<PaymentMethodProps> = ({
  control,
  errors,
  watch,
}) => {
  const { t } = useTranslation();
  const { paymentMethods, addPaymentMethod } = usePaymentMethods();
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedType, setSelectedType] = useState<
    PaymentMethodType | undefined
  >();
  const [showCVV, setShowCVV] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [detectedCardBrand, setDetectedCardBrand] = useState<string>("");
  const cardContentRef = useRef<HTMLDivElement>(null);
  const trueMoneyContentRef = useRef<HTMLDivElement>(null);
  const alipayContentRef = useRef<HTMLDivElement>(null);

  // Credit card form state
  const [creditCardForm, setCreditCardForm] = useState<CreditCardFormData>({
    holderName: "",
    cardNumber: "",
    expiryMonth: "",
    expiryYear: "",
    expiryDate: "",
    cvv: "",
    isDefault: false,
    displayName: "",
    omiseToken: "",
  });

  // TrueMoney form state
  const [trueMoneyForm, setTrueMoneyForm] = useState({
    phoneNumber: "",
    isDefault: false,
    displayName: "",
  });

  // AliPay form state
  const [alipayForm, setAlipayForm] = useState({
    accountEmail: "",
    isDefault: false,
    displayName: "",
  });

  // Watch for payment method changes
  const watchPaymentMethod = watch("paymentMethod");

  // Animation effect for payment method forms
  useEffect(() => {
    if (cardContentRef.current && watchPaymentMethod === "new_credit_card") {
      animateCardExpand(cardContentRef.current);
    }
    if (trueMoneyContentRef.current && watchPaymentMethod === "new_truemoney") {
      animateCardExpand(trueMoneyContentRef.current);
    }
    if (alipayContentRef.current && watchPaymentMethod === "new_alipay") {
      animateCardExpand(alipayContentRef.current);
    }
  }, [watchPaymentMethod]);

  // Helper function to render payment method icons
  const renderPaymentIcon = (type: string, alt?: string) => {
    const iconPath = getPaymentMethodIconPath(type);
    return (
      <img
        src={iconPath}
        alt={alt || type}
        className="h-12 w-12 rounded-lg object-contain"
      />
    );
  };

  // Credit card form handlers
  const handleCreditCardChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type: inputType } = e.target;

    setCreditCardForm((prev) => {
      const newData = {
        ...prev,
        [name]: inputType === "checkbox" ? checked : value,
      };

      if (name === "cardNumber") {
        const formattedValue = formatCreditCard(value);
        newData.cardNumber = formattedValue;
        setDetectedCardBrand(getCardBrand(formattedValue));
      } else if (name === "expiryDate") {
        newData.expiryDate = formatExpiryDate(value);
        // Split the formatted date to extract month and year
        const [month, year] = newData.expiryDate.split("/");
        newData.expiryMonth = month || "";
        newData.expiryYear = year || "";
      } else if (name === "cvv") {
        // Only allow digits for CVV and limit to 4 characters
        const cleanValue = value.replace(/\D/g, "");
        newData.cvv = cleanValue.slice(0, 4);
      } else if (name === "holderName") {
        // Clean holder name - remove extra spaces and capitalize properly
        newData.holderName = value.replace(/\s+/g, " ").trim();
      }

      return newData;
    });

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const toggleCVVVisibility = () => {
    setShowCVV(!showCVV);
  };

  // TrueMoney form handlers
  const handleTrueMoneyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type: inputType } = e.target;

    setTrueMoneyForm((prev) => {
      const newData = {
        ...prev,
        [name]: inputType === "checkbox" ? checked : value,
      };

      // Apply phone number formatting for phoneNumber field
      if (name === "phoneNumber") {
        // Remove non-digit characters and format
        const cleanValue = value.replace(/\D/g, "");
        // Format as XXX-XXX-XXXX for Thai phone numbers
        if (cleanValue.length <= 10) {
          let formatted = cleanValue;
          if (cleanValue.length > 3) {
            formatted = `${cleanValue.slice(0, 3)}-${cleanValue.slice(3)}`;
          }
          if (cleanValue.length > 6) {
            formatted = `${cleanValue.slice(0, 3)}-${cleanValue.slice(3, 6)}-${cleanValue.slice(6)}`;
          }
          newData.phoneNumber = formatted;
        } else {
          newData.phoneNumber = prev.phoneNumber; // Don't allow more than 10 digits
        }
      }

      return newData;
    });

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // AliPay form handlers
  const handleAlipayChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type: inputType } = e.target;

    setAlipayForm((prev) => {
      const newData = {
        ...prev,
        [name]: inputType === "checkbox" ? checked : value,
      };

      // Apply email formatting/cleaning for accountEmail field
      if (name === "accountEmail") {
        // Remove extra spaces and convert to lowercase for email
        newData.accountEmail = value.trim().toLowerCase();
      }

      return newData;
    });

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };
  const handleFormSubmit = async (type: PaymentMethodType, formData: any) => {
    try {
      await addPaymentMethod(type, formData);
      setShowAddForm(false);
      setSelectedType(undefined);
    } catch (error) {
      console.error("Error adding payment method:", error);
    }
  };

  const handleFormCancel = () => {
    setShowAddForm(false);
    setSelectedType(undefined);
  };

  if (showAddForm && selectedType) {
    return (
      <div>
        <PaymentMethodForm
          type={selectedType}
          isEdit={false}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
        />
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-[23px] font-medium text-black">
        {t("payment.methods.title")}
      </h2>
      <p className="mb-4 text-sm text-gray-500">
        การทำธุรกรรมทั้งหมดปลอดภัยและมีการเข้ารหัส
      </p>

      <div className="space-y-4">
        <Controller
          name="paymentMethod"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value}
              onValueChange={(value) => {
                field.onChange(value);
              }}
            >
              {/* Saved Payment Methods */}
              {paymentMethods.length > 0 && (
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-gray-700">
                    {t("payment.methods.current")}
                  </h3>
                  {paymentMethods.map((method) => (
                    <Card
                      key={`saved_${method.id}`}
                      className={`cursor-pointer transition-colors ${
                        field.value === `saved_${method.id}`
                          ? "border-primary bg-primary/5"
                          : "border-gray-200"
                      }`}
                      onClick={(e) =>
                        handleCardClick(e, field.onChange, `saved_${method.id}`)
                      }
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3">
                          <RadioGroupItem
                            value={`saved_${method.id}`}
                            id={`saved_${method.id}`}
                          />
                          <div className="flex items-center space-x-3">
                            {renderPaymentIcon(
                              method.type,
                              getSavedMethodDisplayText(method),
                            )}
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <Label
                                  htmlFor={`saved_${method.id}`}
                                  className="cursor-pointer font-medium"
                                >
                                  {getSavedMethodDisplayText(method)}
                                </Label>
                                {method.isDefault && (
                                  <Badge variant="outline" className="text-xs">
                                    {t("payment.methods.main")}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* New Payment Method Options */}
              <div className="space-y-3">
                {/* Credit/Debit Card */}
                <Card
                  className={`cursor-pointer rounded-[16px] py-0 transition-colors hover:shadow-md ${
                    field.value === "new_credit_card"
                      ? "border-primary-border-card bg-primary/5"
                      : "border-primary-border-card hover:border-gray-300"
                  }`}
                  onClick={(e) =>
                    handleCardClick(e, field.onChange, "new_credit_card")
                  }
                >
                  <CardHeader
                    className={
                      field.value === "new_credit_card"
                        ? "border-primary rounded-t-[16px] border pt-3 pb-2"
                        : "cursor-pointer rounded-[16px] py-6 md:shadow-sm"
                    }
                  >
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem
                        value="new_credit_card"
                        id="new_credit_card"
                      />
                      <div className="flex items-center space-x-3">
                        {renderPaymentIcon("credit_card", "บัตรเครดิต/เดบิต")}
                        <Label
                          htmlFor="new_credit_card"
                          className="cursor-pointer font-medium"
                        >
                          บัตรเครดิต/เดบิต
                        </Label>
                      </div>
                    </div>
                  </CardHeader>
                  {field.value === "new_credit_card" && (
                    <CardContent ref={cardContentRef} className="pt-0 pb-4">
                      <div className="space-y-4">
                        {/* Card Holder Name */}
                        <div className="relative">
                          <InputField
                            id="holderName"
                            name="holderName"
                            placeholder="ชื่อผู้ถือบัตร"
                            value={creditCardForm.holderName}
                            onChange={handleCreditCardChange}
                            error={formErrors.holderName}
                            className="pr-10"
                          />
                          {creditCardForm.holderName && (
                            <button
                              type="button"
                              onClick={() =>
                                setCreditCardForm((prev) => ({
                                  ...prev,
                                  holderName: "",
                                }))
                              }
                              className="absolute top-1/2 right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Card Number */}
                        <div className="relative">
                          <InputField
                            id="cardNumber"
                            name="cardNumber"
                            placeholder="หมายเลขบัตร"
                            value={creditCardForm.cardNumber}
                            onChange={handleCreditCardChange}
                            error={formErrors.cardNumber}
                            className={
                              detectedCardBrand &&
                              detectedCardBrand !== "unknown"
                                ? "pr-16"
                                : "pr-10"
                            }
                            maxLength={19}
                          />
                          {detectedCardBrand &&
                          detectedCardBrand !== "unknown" ? (
                            <div className="absolute top-1/2 right-3 flex -translate-y-1/2 items-center space-x-2">
                              <img
                                src={`src/assets/images/payments/${detectedCardBrand}.png`}
                                alt={detectedCardBrand}
                                className="h-6 w-6 object-contain"
                              />
                              {creditCardForm.cardNumber && (
                                <button
                                  type="button"
                                  onClick={() => {
                                    setCreditCardForm((prev) => ({
                                      ...prev,
                                      cardNumber: "",
                                    }));
                                    setDetectedCardBrand("");
                                  }}
                                  className="rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                                >
                                  <XIcon className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                          ) : (
                            creditCardForm.cardNumber && (
                              <button
                                type="button"
                                onClick={() => {
                                  setCreditCardForm((prev) => ({
                                    ...prev,
                                    cardNumber: "",
                                  }));
                                  setDetectedCardBrand("");
                                }}
                                className="absolute top-1/2 right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                              >
                                <XIcon className="h-4 w-4" />
                              </button>
                            )
                          )}
                        </div>

                        {/* Expiry Date and CVV */}
                        <div className="mb-0 grid grid-cols-2 gap-4">
                          <div className="relative">
                            <InputField
                              id="expiryDate"
                              name="expiryDate"
                              placeholder="MM/YY"
                              value={creditCardForm.expiryDate}
                              onChange={handleCreditCardChange}
                              error={formErrors.expiryDate}
                              maxLength={5}
                            />
                          </div>
                          <div className="relative">
                            <InputField
                              id="cvv"
                              name="cvv"
                              type={showCVV ? "text" : "password"}
                              placeholder="CVV"
                              value={creditCardForm.cvv}
                              onChange={handleCreditCardChange}
                              error={formErrors.cvv}
                              className="pr-10"
                              maxLength={4}
                            />
                            <button
                              type="button"
                              onClick={toggleCVVVisibility}
                              className="absolute top-1/2 right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              {showCVV ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                        </div>

                        {/* Display Name */}
                        <div className="relative">
                          <InputField
                            id="displayName"
                            name="displayName"
                            placeholder="ชื่อที่แสดง (ไม่บังคับ)"
                            value={creditCardForm.displayName}
                            onChange={handleCreditCardChange}
                            className="pr-10"
                          />
                          {creditCardForm.displayName && (
                            <button
                              type="button"
                              onClick={() =>
                                setCreditCardForm((prev) => ({
                                  ...prev,
                                  displayName: "",
                                }))
                              }
                              className="absolute top-1/2 right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Set as Default */}
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="creditCardIsDefault"
                            checked={creditCardForm.isDefault}
                            onCheckedChange={(checked) =>
                              setCreditCardForm((prev) => ({
                                ...prev,
                                isDefault: !!checked,
                              }))
                            }
                          />
                          <Label
                            htmlFor="creditCardIsDefault"
                            className="text-sm"
                          >
                            ตั้งเป็นวิธีการชำระเงินหลัก
                          </Label>
                        </div>
                      </div>
                    </CardContent>
                  )}
                </Card>

                {/* Thai QR */}
                <Card
                  className={`cursor-pointer transition-colors ${
                    field.value === "thai_qr"
                      ? "border-primary bg-primary/5"
                      : "border-gray-200"
                  }`}
                  onClick={(e) => handleCardClick(e, field.onChange, "thai_qr")}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem value="thai_qr" id="thai_qr" />
                      <div className="flex items-center space-x-3">
                        {renderPaymentIcon("thai_qr", "Thai QR Payment")}
                        <Label
                          htmlFor="thai_qr"
                          className="cursor-pointer font-medium"
                        >
                          Thai QR Payment
                        </Label>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Mobile Banking */}
                <Card
                  className={`cursor-pointer transition-colors lg:hidden ${
                    field.value === "mobile_banking"
                      ? "border-primary bg-primary/5"
                      : "border-gray-200"
                  }`}
                  onClick={(e) =>
                    handleCardClick(e, field.onChange, "mobile_banking")
                  }
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem
                        value="mobile_banking"
                        id="mobile_banking"
                      />
                      <div className="flex items-center space-x-3">
                        {renderPaymentIcon("mobile_banking", "Mobile Banking")}
                        <Label
                          htmlFor="mobile_banking"
                          className="cursor-pointer font-medium"
                        >
                          Mobile Banking
                        </Label>
                      </div>
                    </div>

                    {/* Mobile Banking Options */}
                    {field.value === "mobile_banking" && (
                      <div className="mt-4 pl-7">
                        <p className="mb-2 text-sm text-gray-600">
                          เลือกธนาคาร:
                        </p>
                        <div className="grid grid-cols-2 gap-2">
                          {MOBILE_BANKING_OPTIONS.map((bank) => (
                            <div
                              key={bank}
                              className="flex cursor-pointer items-center space-x-2 rounded bg-gray-50 p-2 text-sm hover:bg-gray-100"
                            >
                              <img
                                src={getMobileBankingIconPath(bank)}
                                alt={bank}
                                className="h-12 w-12 rounded-lg object-contain"
                              />
                              <span>{bank}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* TrueMoney Wallet */}
                <Card
                  className={`cursor-pointer rounded-[16px] py-0 transition-colors hover:shadow-md ${
                    field.value === "new_truemoney"
                      ? "border-primary-border-card bg-primary/5"
                      : "border-primary-border-card hover:border-gray-300"
                  }`}
                  onClick={(e) =>
                    handleCardClick(e, field.onChange, "new_truemoney")
                  }
                >
                  <CardHeader
                    className={
                      field.value === "new_truemoney"
                        ? "border-primary rounded-t-[16px] border pt-3 pb-2"
                        : "cursor-pointer rounded-[16px] py-6 md:shadow-sm"
                    }
                  >
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem
                        value="new_truemoney"
                        id="new_truemoney"
                      />
                      <div className="flex items-center space-x-3">
                        {renderPaymentIcon("truemoney", "TrueMoney Wallet")}
                        <Label
                          htmlFor="new_truemoney"
                          className="cursor-pointer font-medium"
                        >
                          TrueMoney Wallet
                        </Label>
                      </div>
                    </div>
                  </CardHeader>
                  {field.value === "new_truemoney" && (
                    <CardContent
                      ref={trueMoneyContentRef}
                      className="pt-0 pb-4"
                    >
                      <div className="space-y-4">
                        {/* Phone Number */}
                        <div className="relative">
                          <InputField
                            id="phoneNumber"
                            name="phoneNumber"
                            placeholder="หมายเลขโทรศัพท์ (XXX-XXX-XXXX)"
                            value={trueMoneyForm.phoneNumber}
                            onChange={handleTrueMoneyChange}
                            error={formErrors.phoneNumber}
                            className="pr-10"
                            maxLength={12}
                          />
                          {trueMoneyForm.phoneNumber && (
                            <button
                              type="button"
                              onClick={() =>
                                setTrueMoneyForm((prev) => ({
                                  ...prev,
                                  phoneNumber: "",
                                }))
                              }
                              className="absolute top-1/2 right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Display Name */}
                        <div className="relative">
                          <InputField
                            id="truemoneyDisplayName"
                            name="displayName"
                            placeholder="ชื่อที่แสดง (ไม่บังคับ)"
                            value={trueMoneyForm.displayName}
                            onChange={handleTrueMoneyChange}
                            className="pr-10"
                          />
                          {trueMoneyForm.displayName && (
                            <button
                              type="button"
                              onClick={() =>
                                setTrueMoneyForm((prev) => ({
                                  ...prev,
                                  displayName: "",
                                }))
                              }
                              className="absolute top-1/2 right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Set as Default */}
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="truemoneyIsDefault"
                            checked={trueMoneyForm.isDefault}
                            onCheckedChange={(checked) =>
                              setTrueMoneyForm((prev) => ({
                                ...prev,
                                isDefault: !!checked,
                              }))
                            }
                          />
                          <Label
                            htmlFor="truemoneyIsDefault"
                            className="text-sm"
                          >
                            ตั้งเป็นวิธีการชำระเงินหลัก
                          </Label>
                        </div>
                      </div>
                    </CardContent>
                  )}
                </Card>

                {/* AliPay */}
                <Card
                  className={`cursor-pointer rounded-[16px] py-0 transition-colors hover:shadow-md ${
                    field.value === "new_alipay"
                      ? "border-primary-border-card bg-primary/5"
                      : "border-primary-border-card hover:border-gray-300"
                  }`}
                  onClick={(e) =>
                    handleCardClick(e, field.onChange, "new_alipay")
                  }
                >
                  <CardHeader
                    className={
                      field.value === "new_alipay"
                        ? "border-primary rounded-t-[16px] border pt-3 pb-2"
                        : "cursor-pointer rounded-[16px] py-6 md:shadow-sm"
                    }
                  >
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem value="new_alipay" id="new_alipay" />
                      <div className="flex items-center space-x-3">
                        {renderPaymentIcon("alipay", "AliPay")}
                        <Label
                          htmlFor="new_alipay"
                          className="cursor-pointer font-medium"
                        >
                          AliPay
                        </Label>
                      </div>
                    </div>
                  </CardHeader>
                  {field.value === "new_alipay" && (
                    <CardContent ref={alipayContentRef} className="pt-0 pb-4">
                      <div className="space-y-4">
                        {/* Account Email */}
                        <div className="relative">
                          <InputField
                            id="accountEmail"
                            name="accountEmail"
                            type="email"
                            placeholder="อีเมลบัญชี AliPay"
                            value={alipayForm.accountEmail}
                            onChange={handleAlipayChange}
                            error={formErrors.accountEmail}
                            className="pr-10"
                          />
                          {alipayForm.accountEmail && (
                            <button
                              type="button"
                              onClick={() =>
                                setAlipayForm((prev) => ({
                                  ...prev,
                                  accountEmail: "",
                                }))
                              }
                              className="absolute top-1/2 right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Display Name */}
                        <div className="relative">
                          <InputField
                            id="alipayDisplayName"
                            name="displayName"
                            placeholder="ชื่อที่แสดง (ไม่บังคับ)"
                            value={alipayForm.displayName}
                            onChange={handleAlipayChange}
                            className="pr-10"
                          />
                          {alipayForm.displayName && (
                            <button
                              type="button"
                              onClick={() =>
                                setAlipayForm((prev) => ({
                                  ...prev,
                                  displayName: "",
                                }))
                              }
                              className="absolute top-1/2 right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Set as Default */}
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="alipayIsDefault"
                            checked={alipayForm.isDefault}
                            onCheckedChange={(checked) =>
                              setAlipayForm((prev) => ({
                                ...prev,
                                isDefault: !!checked,
                              }))
                            }
                          />
                          <Label htmlFor="alipayIsDefault" className="text-sm">
                            ตั้งเป็นวิธีการชำระเงินหลัก
                          </Label>
                        </div>
                      </div>
                    </CardContent>
                  )}
                </Card>
              </div>
            </RadioGroup>
          )}
        />

        {errors.paymentMethod && (
          <p className="mt-2 text-xs text-red-600">
            {getPaymentMethodErrorMessage(errors.paymentMethod)}
          </p>
        )}
      </div>
    </div>
  );
};

export default PaymentMethod;
