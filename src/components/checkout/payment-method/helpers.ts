import React from "react";
import type { BasePaymentMethod } from "@/types/payment-methods";
import type { PaymentOption } from "./types";
import { gsap } from "gsap";

// Payment method icons
import visaIcon from "@/assets/images/payments/visa.png";
import mastercardIcon from "@/assets/images/payments/mastercard.png";
import amexIcon from "@/assets/images/payments/amex.png";
import jcbIcon from "@/assets/images/payments/jcb.png";
import truemoneyIcon from "@/assets/images/payments/truemoney.png";
import alipayIcon from "@/assets/images/payments/alipay.png";
import thaiQrIcon from "@/assets/images/payments/thai-qr.png";
import paypalIcon from "@/assets/images/payments/paypal.png";

// Mobile banking icons
import scbIcon from "@/assets/images/mobile-banking/scb.webp";
import kplusIcon from "@/assets/images/mobile-banking/kplus.webp";
import bualuangIcon from "@/assets/images/mobile-banking/bualuang.webp";
import ktbNextIcon from "@/assets/images/mobile-banking/ktb-next.webp";
import kmaIcon from "@/assets/images/mobile-banking/kma.webp";

/**
 * Helper function to handle card clicks without interfering with form elements
 * Prevents double-triggering when clicking on radio buttons or other form elements
 */
export const handleCardClick = (
  e: React.MouseEvent<HTMLDivElement>,
  onChange: (value: string) => void,
  value: string,
) => {
  // Prevent card click when clicking on form elements
  const target = e.target as HTMLElement;
  if (
    target.tagName === "INPUT" ||
    target.tagName === "SELECT" ||
    target.tagName === "TEXTAREA" ||
    target.tagName === "BUTTON" ||
    target.closest(
      'input, select, textarea, button, [role="combobox"], [role="listbox"]',
    )
  ) {
    return;
  }
  onChange(value);
};

/**
 * Gets error message for payment method fields
 */
export const getPaymentMethodErrorMessage = (
  error: any,
): string | undefined => {
  return error?.message;
};

/**
 * Gets the appropriate icon path for a payment method type
 */
export const getPaymentMethodIconPath = (type: string): string => {
  switch (type) {
    case "credit_card":
    case "debit_card":
      return visaIcon; // Default to Visa for generic card
    case "truemoney":
      return truemoneyIcon;
    case "alipay":
      return alipayIcon;
    case "thai_qr":
    case "promptpay":
      return thaiQrIcon;
    case "mobile_banking":
      return kplusIcon;
    case "paypal":
      return paypalIcon;
    case "visa":
      return visaIcon;
    case "mastercard":
      return mastercardIcon;
    case "amex":
      return amexIcon;
    case "jcb":
      return jcbIcon;
    default:
      return visaIcon;
  }
};

/**
 * Gets mobile banking icon path by bank name
 */
export const getMobileBankingIconPath = (bankName: string): string => {
  const bankMap: Record<string, string> = {
    "scb": scbIcon,
    "kplus": kplusIcon,
    "bbm": bualuangIcon,
    "ktb": ktbNextIcon,
    "kma": kmaIcon,
  };

  return bankMap[bankName] || scbIcon;
};

/**
 * Creates payment options from saved payment methods and available methods
 */
export const createPaymentOptions = (
  savedMethods: BasePaymentMethod[],
  t: (key: string) => string,
): PaymentOption[] => {
  const options: PaymentOption[] = [];

  // Add saved payment methods
  savedMethods.forEach((method) => {
    options.push({
      id: `saved_${method.id}`,
      type: method.type,
      name: method.displayName,
      description: method.isDefault ? t("payment.methods.main") : undefined,
      savedMethod: method,
    });
  });

  // Add new payment method options
  const newMethodOptions: PaymentOption[] = [
    {
      id: "new_credit_card",
      type: "credit_card",
      name: t("payment.methods.addCredit"),
      description: t("payment.methods.creditDescription"),
    },
    {
      id: "thai_qr",
      type: "thai_qr",
      name: "Thai QR Payment",
      description: "ชำระผ่าน QR Code",
    },
    {
      id: "mobile_banking",
      type: "mobile_banking",
      name: "Mobile Banking",
      description: "ชำระผ่านแอปธนาคาร",
    },
    {
      id: "new_truemoney",
      type: "truemoney",
      name: "TrueMoney Wallet",
      description: "เพิ่มกระเป๋าเงิน TrueMoney ใหม่",
    },
    {
      id: "new_alipay",
      type: "alipay",
      name: "AliPay",
      description: "เพิ่มบัญชี AliPay ใหม่",
    },
  ];

  options.push(...newMethodOptions);

  return options;
};

/**
 * Gets display text for saved payment method
 */
export const getSavedMethodDisplayText = (
  method: BasePaymentMethod,
): string => {
  switch (method.type) {
    case "credit_card":
    case "debit_card":
      return method.displayName;
    case "truemoney":
      return `TrueMoney ${method.displayName}`;
    case "alipay":
      return `AliPay ${method.displayName}`;
    default:
      return method.displayName;
  }
};

/**
 * Animates card content expansion with GSAP
 */
export const animateCardExpand = (element: HTMLElement) => {
  gsap.set(element, {
    height: 0,
    opacity: 0,
    overflow: "hidden",
    transformOrigin: "top center",
  });

  gsap.to(element, {
    height: "auto",
    opacity: 1,
    duration: 0.6,
    ease: "power2.out",
    onComplete: () => {
      gsap.set(element, { overflow: "visible" });
    },
  });
};
